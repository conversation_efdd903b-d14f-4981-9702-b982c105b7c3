import { useCallback, useEffect, useMemo, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { transformDataFromHoaDon } from '../utils/transform-data';
import { useAuth } from '@/contexts/auth-context';
import { TaiKhoan } from '@/types/schemas';
import { QUERY_KEYS } from '@/constants';
import { useCRUD } from '@/hooks';
import api from '@/lib/api';

export function useInvoiceParamsPresence() {
  const searchParams = useSearchParams();
  return useMemo(() => Boolean(searchParams.get('hdbh') || searchParams.get('hdbdv')), [searchParams]);
}

interface UseMultipleFormsArgs {
  addItem: (data: any) => Promise<any>;
  editData?: (uuid: string, data: any) => Promise<any>;
}

export function useMultipleForms({ addItem, editData }: UseMultipleFormsArgs) {
  const searchParams = useSearchParams();
  const router = useRouter();
  const { entity } = useAuth();

  const hasInvoiceParams = useInvoiceParamsPresence();

  // Get mode parameter from URL
  const mode = searchParams.get('mode');

  const [invoiceData, setInvoiceData] = useState<any[]>([]);
  const [showMultipleForms, setShowMultipleForms] = useState<boolean>(hasInvoiceParams);
  const [currentFormIndex, setCurrentFormIndex] = useState<number>(0);
  const [existingGiayBaoCoData, setExistingGiayBaoCoData] = useState<any[]>([]);

  // Fetch tai khoan for default tk_data in initial form data
  const taiKhoanSearchData = useMemo(() => ({ prefix: '1211' }), []);
  const { data: taiKhoan, isLoading: isLoadingTaiKhoan } = useCRUD<TaiKhoan, any>({
    endpoint: QUERY_KEYS.TAI_KHOAN,
    searchData: taiKhoanSearchData
  });

  // Function to fetch invoice data by UUID
  const fetchInvoiceByUuid = useCallback(
    async (uuid: string, isHoaDonBanHang: boolean = false) => {
      if (!entity?.slug) return null;

      try {
        const endpoint = isHoaDonBanHang ? QUERY_KEYS.HOA_DON_BAN_HANG : QUERY_KEYS.HOA_DON_BAN_DICH_VU;
        const response = await api.get(`/entities/${entity.slug}/erp/${endpoint}/${uuid}/`);
        return response.data;
      } catch (error) {
        return null;
      }
    },
    [entity?.slug]
  );

  // Function to fetch existing giấy báo có data by UUID
  const fetchGiayBaoCoByUuid = useCallback(
    async (uuid: string) => {
      if (!entity?.slug) return null;

      try {
        const response = await api.get(`/entities/${entity.slug}/erp/${QUERY_KEYS.GIAY_BAO_CO}/${uuid}/`);
        return response.data;
      } catch (error) {
        return null;
      }
    },
    [entity?.slug]
  );

  // Handle URL params for creating phieu thu from invoices
  useEffect(() => {
    const hdbhParam = searchParams.get('hdbh');
    const hdbdvParam = searchParams.get('hdbdv');

    if (hdbhParam || hdbdvParam) {
      setShowMultipleForms(true);

      const invoiceUuids = (hdbhParam || hdbdvParam)?.split(',').filter(Boolean) || [];
      const isHoaDonBanHang = !!hdbhParam;

      if (invoiceUuids.length > 0) {
        Promise.all(invoiceUuids.map(uuid => fetchInvoiceByUuid(uuid, isHoaDonBanHang)))
          .then(async invoices => {
            const validInvoices = invoices.filter(Boolean);
            setInvoiceData(validInvoices);

            // If in edit mode, also fetch existing giấy báo có data
            if (mode === 'edit' && validInvoices.length > 0) {
              const giayBaoCoPromises = validInvoices.map(invoice => {
                const giayBaoCoUuid = invoice.giay_bao_co_uuids?.[0];
                return giayBaoCoUuid ? fetchGiayBaoCoByUuid(giayBaoCoUuid) : null;
              });

              const existingGiayBaoCos = await Promise.all(giayBaoCoPromises);
              setExistingGiayBaoCoData(existingGiayBaoCos.filter(Boolean));
            }

            if (validInvoices.length > 0) {
              setCurrentFormIndex(0);
            }
          })
          .catch(error => {
            console.error('Error fetching invoices:', error);
          });
      }
    }
  }, [searchParams, entity?.slug, fetchInvoiceByUuid, fetchGiayBaoCoByUuid, mode]);

  // Multiple forms submit handler
  const handleMultipleFormSubmit = useCallback(
    async (data: any) => {
      try {
        if (mode === 'edit' && editData) {
          // In edit mode, we need to get the UUID from the current invoice data
          const currentInvoice = invoiceData[currentFormIndex];
          const existingGiayBaoCo = existingGiayBaoCoData[currentFormIndex];

          if (currentInvoice?.giay_bao_co_uuids?.[0] && existingGiayBaoCo) {
            // Include old giấy báo có data in the submission
            const editDataWithOldValues = {
              ...data,
              // Include old values from existing giấy báo có
              i_so_ct: existingGiayBaoCo.i_so_ct,
              so_ct: existingGiayBaoCo.so_ct,
              ma_nk: existingGiayBaoCo.ma_nk,
              ngay_lct: existingGiayBaoCo.ngay_lct
            };

            await editData(currentInvoice.giay_bao_co_uuids[0], editDataWithOldValues);
          }
        } else {
          await addItem(data);
        }

        if (currentFormIndex < invoiceData.length - 1) {
          setShowMultipleForms(true);
          setCurrentFormIndex(currentFormIndex + 1);
        } else {
          setShowMultipleForms(false);
          setInvoiceData([]);
          setCurrentFormIndex(0);
          setExistingGiayBaoCoData([]);
          router.back();
        }
      } catch (error) {
        return;
      }
    },
    [addItem, editData, mode, currentFormIndex, invoiceData, existingGiayBaoCoData, router]
  );

  // Close handler for multiple forms
  const handleCloseMultipleForms = useCallback(() => {
    setShowMultipleForms(false);
    setInvoiceData([]);
    setCurrentFormIndex(0);
    setExistingGiayBaoCoData([]);
  }, []);

  // Build initial form data from current invoice + default tk_data
  let initialData = useMemo(
    () => transformDataFromHoaDon(invoiceData[currentFormIndex]),
    [invoiceData, currentFormIndex]
  );

  if (!isLoadingTaiKhoan && taiKhoan && taiKhoan.length > 0) {
    initialData = {
      ...initialData,
      tk_data: taiKhoan[0] as any
    } as any;
  }

  return {
    hasInvoiceParams,
    showMultipleForms,
    invoiceData,
    currentFormIndex,
    initialData,
    handleMultipleFormSubmit,
    handleCloseMultipleForms,
    formMode: mode === 'edit' ? 'edit' : 'add'
  } as const;
}
