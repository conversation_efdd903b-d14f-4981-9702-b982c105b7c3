import { useState } from 'react';

/**
 * Hook for managing form state
 * @template T The type of the selected object (must have pt_tao_yn property)
 * @template K The type of the row ID (string | number)
 * @returns Form state and functions to manage it
 */
export const useForm = () => {
  const [showForm, setShowForm] = useState<boolean>(false);
  const [showSearch, setShowSearch] = useState<boolean>(false);
  const [showDelete, setShowDelete] = useState<boolean>(false);
  const [showCantEdit, setShowCantEdit] = useState<boolean>(false);
  const [formMode, setFormMode] = useState<'add' | 'edit' | 'view'>('add');
  const [selectedObj, setSelectedObj] = useState<any | null>(null);
  const [selectedRowIndex, setSelectedRowIndex] = useState<any | null>(null);
  const [isCopyMode, setIsCopyMode] = useState<boolean>(false);

  /**
   * <PERSON><PERSON> closing the form
   */
  const handleCloseForm = () => {
    setShowForm(false);
  };

  /**
   * <PERSON><PERSON> closing the delete confirmation dialog
   */
  const handleCloseDelete = () => {
    setShowDelete(false);
  };

  /**
   * Handle clicking the add button
   */
  const handleAddClick = () => {
    setFormMode('add');
    setIsCopyMode(false);
    setShowForm(true);
  };

  /**
   * Handle clicking the edit button
   */
  const handleEditClick = () => {
    if (!selectedObj?.pt_tao_yn && selectedObj?.giay_bao_co_uuids[0]) {
      setShowCantEdit(true);
      return;
    } else {
      setFormMode('edit');
      setShowForm(true);
    }
  };

  /**
   * Handle clicking the view button
   */
  const handleViewClick = () => {
    setFormMode('view');
    setShowForm(true);
  };

  /**
   * Handle clicking the delete button
   */
  const handleDeleteClick = () => {
    setShowDelete(true);
  };

  /**
   * Handle clicking the copy button
   */
  const handleCopyClick = () => {
    setFormMode('add');
    setIsCopyMode(true);
    setShowForm(true);
  };

  /**
   * Handle clicking a row in the data table
   * @param params The row parameters containing row data and id
   */
  const handleRowClick = (params: any) => {
    setSelectedObj(params.row);
    setSelectedRowIndex(params.id);
  };

  /**
   * Clear the selection
   */
  const clearSelection = () => {
    setSelectedObj(null);
    setSelectedRowIndex(null);
  };

  const handleSearch = () => {
    setShowSearch(true);
  };

  const handleCloseSearch = () => {
    setShowSearch(false);
  };

  return {
    showForm,
    showSearch,
    showDelete,
    formMode,
    selectedObj,
    selectedRowIndex,
    isCopyMode,
    showCantEdit,
    setShowCantEdit,

    handleCloseForm,
    handleCloseDelete,
    handleAddClick,
    handleEditClick,
    handleViewClick,
    handleDeleteClick,
    handleCopyClick,
    handleRowClick,
    clearSelection,
    handleSearch,
    handleCloseSearch
  };
};
