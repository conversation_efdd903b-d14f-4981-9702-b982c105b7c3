'use client';

import { useRouter } from 'next/navigation';
import { useState } from 'react';
import Split from 'react-split';
import { InputTable, AritoDataTables, DeleteDialog, LoadingOverlay } from '@/components/custom/arito';
import { SearchDialog, ActionBar, FormDialog, InputTableAction } from './components';
import ConfirmationDialog from '@/components/custom/arito/dialog/confirm-dialog';
import { getDataTableColumns, getInputTableColumns } from './cols-definition';
import { useFormState, useRows, useHoaDonBanHang, useCRUD } from '@/hooks';
import { useHoaDonBanHangDetail } from './hooks/useHoaDonBanHangDetail';
import { useForm, useLoading } from './hooks';
import { QUERY_KEYS } from '@/constants';

export default function HoaDonDieuChinhGiaHangBanPage() {
  const router = useRouter();
  const [showSearchDialog, setShowSearchDialog] = useState(false);
  const {
    hoaDonBanHangs,
    isLoading,
    addHoaDonBanHang,
    updateHoaDonBanHang,
    deleteHoaDonBanHang,
    refreshHoaDonBanHangs
  } = useHoaDonBanHang();
  const { deleteItem: deleteGiayBaoCo } = useCRUD<any, any>({ endpoint: QUERY_KEYS.GIAY_BAO_CO, initialFetch: false });
  const { deleteItem: deletePhieuThu } = useCRUD<any, any>({ endpoint: QUERY_KEYS.PHIEU_THU, initialFetch: false });
  const { selectedObj, selectedRowIndex, handleRowClick, clearSelection } = useRows();
  const {
    showForm,
    showDelete,
    formMode,
    isCopyMode,
    showCantEdit,
    setShowCantEdit,
    handleCloseForm,
    handleCloseDelete,
    handleAddClick,
    handleEditClick,
    handleViewClick,
    handleDeleteClick,
    handleCopyClick
  } = useForm();
  const { loading } = useLoading({
    isOpen: showForm,
    external: false,
    duration: 500
  });
  const { detail, fetchDetail } = useHoaDonBanHangDetail(selectedObj?.uuid);

  const handleSearch = () => {
    setShowSearchDialog(true);
  };

  const handleSearchClose = () => {
    setShowSearchDialog(false);
  };

  const handleSearchSubmit = (filters: any) => {
    console.log('Search filters:', filters);
    // TODO: Implement search functionality here
  };

  const handleSubmit = async (data: any) => {
    try {
      if (formMode === 'add') {
        const result = await addHoaDonBanHang(data);

        if (result?.uuid && data.pt_tao_yn && data.ma_httt === 'CKB')
          router.push(`/${QUERY_KEYS.GIAY_BAO_CO}?hdbh=${result?.uuid}`);
        if (result?.uuid && data.pt_tao_yn && data.ma_httt === 'TMB')
          router.push(`/${QUERY_KEYS.PHIEU_THU}?hdbh=${result?.uuid}`);

      } else if (formMode === 'edit' && selectedObj) {
        await updateHoaDonBanHang(selectedObj.uuid, data);

        if (data.pt_tao_yn && data.ma_httt === 'CKB' && !selectedObj.giay_bao_co_uuids[0])
          router.push(`/${QUERY_KEYS.GIAY_BAO_CO}?hdbh=${selectedObj.uuid}`);
        else if (
          !data.pt_tao_yn &&
          selectedObj.ma_httt === 'CKB' &&
          selectedObj.pt_tao_yn &&
          selectedObj.giay_bao_co_uuids[0]
        )
          await deleteGiayBaoCo(selectedObj.giay_bao_co_uuids[0]);
        else if (data.pt_tao_yn && data.ma_httt === 'CKB' && selectedObj.giay_bao_co_uuids[0])
          router.push(`/${QUERY_KEYS.GIAY_BAO_CO}?hdbh=${selectedObj.uuid}&mode=edit`);

        else if (data.pt_tao_yn && data.ma_httt === 'TMB' && !selectedObj.phieu_thu_uuids[0])
          router.push(`/${QUERY_KEYS.PHIEU_THU}?hdbh=${selectedObj.uuid}`);
        else if (
          !data.pt_tao_yn &&
          selectedObj.ma_httt === 'TMB' &&
          selectedObj.pt_tao_yn &&
          selectedObj.phieu_thu_uuids[0]
        )
          await deletePhieuThu(selectedObj.phieu_thu_uuids[0]);
        else if (data.pt_tao_yn && data.ma_httt === 'CKB' && selectedObj.phieu_thu_uuids[0])
          router.push(`/${QUERY_KEYS.PHIEU_THU}?hdbh=${selectedObj.uuid}&mode=edit`);

        handleCloseForm();
        clearSelection();
        refreshHoaDonBanHangs();
      }
    } catch (error) {
      return;
    }
  };

  const tables = [
    {
      name: 'Tất cả',
      rows: hoaDonBanHangs,
      columns: getDataTableColumns(handleViewClick)
    },
    {
      name: 'Lập chứng từ',
      rows: hoaDonBanHangs.filter(row => row.status === '0'),
      columns: getDataTableColumns(handleViewClick),
      icon: <div className='mr-2 size-[7px] rounded-full bg-[#EAD1DC]' />
    },
    {
      name: 'Chờ duyệt',
      rows: hoaDonBanHangs.filter(row => row.status === '3'),
      columns: getDataTableColumns(handleViewClick),
      icon: <div className='mr-2 size-[7px] rounded-full bg-red-500' />
    },
    {
      name: 'Xóa hóa đơn',
      rows: hoaDonBanHangs.filter(row => row.status === '5'),
      columns: getDataTableColumns(handleViewClick),
      icon: <div className='mr-2 size-[7px] rounded-full bg-[#3D85C6]' />
    },
    {
      name: 'Khác',
      rows: hoaDonBanHangs.filter(row => row.status === '99'),
      columns: getDataTableColumns(handleViewClick),
      icon: <div className='mr-2 size-[7px] rounded-full bg-black' />
    }
  ];

  return (
    <div className='flex h-full min-h-[calc(100vh-120px)] w-full flex-col overflow-hidden'>
      {showSearchDialog && (
        <SearchDialog open={showSearchDialog} onClose={handleSearchClose} onSearch={handleSearchSubmit} />
      )}

      {loading && (
        <div className='absolute inset-0 z-50 flex items-center justify-center bg-white bg-opacity-80'>
          <LoadingOverlay />
        </div>
      )}

      {showForm && (
        <FormDialog
          formMode={formMode}
          open={showForm}
          isCopyMode={isCopyMode}
          onClose={handleCloseForm}
          initialData={formMode === 'add' && !isCopyMode ? undefined : selectedObj}
          onSubmit={handleSubmit}
          onAdd={handleAddClick}
          onEdit={handleEditClick}
          onDelete={handleDeleteClick}
          onCopy={handleCopyClick}
        />
      )}

      {showDelete && (
        <DeleteDialog
          open={showDelete}
          onClose={handleCloseDelete}
          selectedObj={selectedObj}
          deleteObj={deleteHoaDonBanHang}
          clearSelection={clearSelection}
        />
      )}
      {showCantEdit && (
        <ConfirmationDialog
          open={showCantEdit}
          onClose={() => setShowCantEdit(false)}
          onConfirm={() => setShowCantEdit(false)}
          message='Hóa đơn đã thanh toán, không thể sửa'
        />
      )}

      {!showForm && (
        <>
          <ActionBar
            onAdd={handleAddClick}
            onEdit={handleEditClick}
            onDelete={handleDeleteClick}
            onCopy={handleCopyClick}
            onSearch={handleSearch}
            onRefresh={async () => {
              await refreshHoaDonBanHangs();
              await fetchDetail();
            }}
            isEditDisabled={!selectedObj}
          />

          {isLoading && (
            <div className='flex h-full items-center justify-center'>
              <LoadingOverlay />
            </div>
          )}

          {!isLoading && (
            <Split
              className='flex flex-1 flex-col overflow-hidden'
              direction='vertical'
              sizes={[50, 50]}
              minSize={200}
              gutterSize={4}
              gutterAlign='center'
              snapOffset={30}
              dragInterval={1}
              cursor='row-resize'
            >
              <div className='w-full overflow-hidden'>
                <AritoDataTables
                  tables={tables}
                  onRowClick={handleRowClick}
                  selectedRowId={selectedRowIndex || undefined}
                />
              </div>

              <div className='max-h-[300px] overflow-hidden'>
                <InputTable
                  rows={detail || []}
                  columns={getInputTableColumns()}
                  mode={formMode}
                  actionButtons={
                    <InputTableAction
                      formMode={formMode}
                      handleExport={() => console.log('Export clicked')}
                      handlePin={() => console.log('Pin clicked')}
                    />
                  }
                />
              </div>
            </Split>
          )}
        </>
      )}
    </div>
  );
}
