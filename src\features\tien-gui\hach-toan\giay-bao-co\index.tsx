'use client';

import Split from 'react-split';
import { exportCreditAdviceDetailColumns, getChangingValueCreditAdviceColumns } from './cols-definition';
import { InputTable, AritoDataTables, LoadingOverlay, DeleteDialog } from '@/components/custom/arito';
import { useInvoiceParamsPresence, useMultipleForms } from './hooks/useMultipleForms';
import InputTableActionBar from './components/InputTableActionBar';
import { ActionBar, AddForm, SearchDialog } from './components';
import { useFormState, useRows, useCRUD } from '@/hooks';
import { QUERY_KEYS } from '@/constants';

export default function GiayBaoCoPage() {
  const hasInvoiceParams = useInvoiceParamsPresence();

  const { selectedObj, handleRowClick, clearSelection, selectedRowIndex } = useRows();
  const { handleViewClick } = useFormState();

  // Use CRUD with server-side pagination for AritoDataTables
  const { data, addItem, updateItem, deleteItem, refreshData, totalItems, currentPage, handlePageChange, isLoading } =
    useCRUD<any, any>({ endpoint: QUERY_KEYS.GIAY_BAO_CO, initialFetch: !hasInvoiceParams });

  const tables = [
    {
      name: 'Tất cả',
      rows: data || [],
      columns: getChangingValueCreditAdviceColumns(handleViewClick)
    }
  ];

  const {
    showForm,
    showSearch,
    showDelete,
    formMode,
    isCopyMode,

    handleCloseForm,
    handleCloseDelete,
    handleAddClick,
    handleEditClick,
    handleDeleteClick,
    handleCopyClick,

    handleSearch,
    handleCloseSearch
  } = useFormState();

  const {
    showMultipleForms,
    initialData,
    handleMultipleFormSubmit,
    handleCloseMultipleForms,
    invoiceData,
    currentFormIndex,
    formMode: multipleFormsMode
  } = useMultipleForms({ addItem, editData: updateItem });

  const handleFormSubmit = async (data: any) => {
    try {
      if (formMode === 'add') {
        await addItem(data);
      } else if (formMode === 'edit' && selectedObj) {
        await updateItem(selectedObj.uuid, data);
      }

      handleCloseForm();
      clearSelection();
      await refreshData();
    } catch (error) {
      return;
    }
  };

  const handleSearchSubmit = (filters: any) => {};

  return (
    <>
      <div className='flex h-full min-h-[calc(100vh-64px)] w-screen flex-col overflow-auto'>
        {/* Multiple forms for creating phieu thu from invoices */}
        {showMultipleForms && invoiceData.length > 0 && (
          <>
            <AddForm
              formMode={multipleFormsMode}
              initialData={initialData}
              onSubmit={handleMultipleFormSubmit}
              onClose={handleCloseMultipleForms}
              currentIndex={currentFormIndex}
              totalCount={invoiceData.length}
            />
          </>
        )}

        {/* Regular form */}
        {showForm && !showMultipleForms && (
          <AddForm
            formMode={formMode}
            initialData={formMode === 'add' && !isCopyMode ? undefined : selectedObj}
            onSubmit={handleFormSubmit}
            onClose={handleCloseForm}
          />
        )}

        {showDelete && (
          <DeleteDialog
            open={showDelete}
            onClose={handleCloseDelete}
            selectedObj={selectedObj}
            deleteObj={deleteItem}
            clearSelection={clearSelection}
          />
        )}

        {!showForm && !showMultipleForms && (
          <>
            <SearchDialog
              onCloseSearchDialog={handleCloseSearch}
              openSearchDialog={showSearch}
              onSearch={handleSearchSubmit}
            />

            <ActionBar
              onAddClick={handleAddClick}
              onEditClick={() => selectedObj && handleEditClick()}
              onDeleteClick={() => selectedObj && handleDeleteClick()}
              onCopyClick={() => selectedObj && handleCopyClick()}
              onSearchClick={handleSearch}
              onRefreshClick={async () => {
                await refreshData();
              }}
            />
            <Split
              className='flex flex-1 flex-col'
              direction='vertical'
              sizes={[50, 50]}
              minSize={200}
              gutterSize={8}
              gutterAlign='center'
              snapOffset={30}
              dragInterval={1}
              cursor='row-resize'
            >
              <div className='w-full overflow-hidden'>
                {isLoading && <LoadingOverlay />}
                {!isLoading && (
                  <AritoDataTables
                    tables={tables}
                    onRowClick={handleRowClick}
                    selectedRowId={selectedRowIndex || undefined}
                    totalItems={totalItems}
                    currentPage={currentPage}
                    onPageChange={handlePageChange}
                    serverSidePagination
                  />
                )}
              </div>

              <div className='w-full overflow-hidden'>
                <InputTable
                  rows={selectedObj?.chi_tiet_giay_bao_co_data || []}
                  columns={exportCreditAdviceDetailColumns}
                  mode={formMode}
                  getRowId={row => row?.uuid || ''}
                  className='w-full'
                  actionButtons={<InputTableActionBar mode={formMode} />}
                />
              </div>
            </Split>
          </>
        )}
      </div>
    </>
  );
}
