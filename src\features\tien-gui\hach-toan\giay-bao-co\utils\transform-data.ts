import { transformDocumentNumber } from '@/components/custom/arito/form/document-number/util';
import { calculatePaymentTotals } from './calc-utils';
import { MA_CHUNG_TU } from '@/constants';
import { FormFieldState } from '../hooks';

/**
 * Transform detail rows for API submission
 * @param detailRows - Array of detail row data from the form
 * @returns Transformed detail rows ready for API submission
 */
export const transformDetailRows = (detailRows: any[], fallbackDienGiai?: string) => {
  return detailRows.map((row: any, index: number) => ({
    line: index + 1,
    dien_giai: row.dien_giai || fallbackDienGiai || '',

    ma_kh: row.ma_kh_data?.uuid || row.ma_kh || '',

    id_hd: row.id_hd_data?.ID || row.id_hd || '',

    tk_co: row.tk_co_data?.uuid || row.tk_co || row.id_hd_data?.tk_data?.uuid || '',

    ty_gia_hd: row.ty_gia_hd || 1,
    ty_gia2: row.id_hd_data?.ty_gia || row.ty_gia_hd || 0,
    tien_nt: row.id_hd_data?.tien_hd_nt || row.tien_nt || 0,
    tien: row.id_hd_data?.tien_tren_hd || row.tien_hd_nt || 0,

    ma_bp: row.ma_bp_data?.uuid || row.ma_bp || '',

    ma_vv: row.ma_vv_data?.uuid || row.ma_vv || '',
    ma_hd: row.ma_hd_data?.uuid || row.ma_hd || '',
    ma_dtt: row.ma_dtt_data?.uuid || row.ma_dtt || '',
    ma_ku: row.ma_ku_data?.uuid || row.ma_ku || '',
    ma_phi: row.ma_phi_data?.uuid || row.ma_phi || '',
    ma_sp: row.ma_sp_data?.uuid || row.ma_sp || '',
    ma_lsx: row.ma_lsx_data?.uuid || row.ma_lsx || '',
    ma_cp0: row.ma_cp0_data?.uuid || row.ma_cp0 || '',

    id_tt: row.id_tt || 0
  }));
};

/**
 * Transform bank fee rows for API submission
 * @param bankFeeRows - Array of bank fee row data from the form
 * @returns Transformed bank fee rows ready for API submission
 */
export const transformBankFeeRows = (bankFeeRows: any[]) => {
  return bankFeeRows.map((row: any, index: number) => ({
    line: index + 1,

    ma_kh: row.ma_kh_data?.uuid || row.ma_kh || '',
    ma_cpnh: row.ma_cpnh_data?.uuid || row.ma_cpnh || '',
    tien_cp_nt: row.tien_cp_nt || 0,
    tien_cp: row.tien_cp_nt || 0,
    so_ct0: row.so_ct0_data?.uuid || row.so_ct0 || '',
    so_ct2: row.so_ct2_data?.uuid || row.so_ct2 || '',
    ngay_ct0: row.ngay_ct0 || '',
    dien_giai: row.dien_giai || '',
    tk_cpnh: row.tk_cpnh_data?.uuid || row.tk_cpnh || '',
    ten_tk_cpnh: row.tk_cpnh_data?.name || '',
    tk_du: row.tk_du_data?.uuid || row.tk_du || '',
    ten_tk_du: row.tk_du_data?.name || '',
    tk_thue: row.ma_cpnh_data?.tk_cpnh || row.tk_thue || '',
    ma_thue: row.ma_cpnh_data?.ma_thue || row.ma_thue || '',
    thue_suat: row.thue_suat || 0,
    t_thue_nt: row.t_thue_nt || 0,
    t_thue: row.t_thue_nt || 0,
    ma_bp: row.ma_bp_data?.uuid || row.ma_bp || '',
    ma_vv: row.ma_vv_data?.uuid || row.ma_vv || '',
    ma_hd: row.ma_hd_data?.uuid || row.ma_hd || '',
    ma_dtt: row.ma_dtt_data?.uuid || row.ma_dtt || '',
    ma_ku: row.ma_ku_data?.uuid || row.ma_ku || '',
    ma_phi: row.ma_phi_data?.uuid || row.ma_phi || '',
    ma_sp: row.ma_sp_data?.uuid || row.ma_sp || '',
    ma_lsx: row.ma_lsx || '',
    ma_cp0: row.ma_cp0_data?.uuid || row.ma_cp0 || ''
  }));
};

/**
 * Transform all form data for submission
 * @param data - Form data from the form submission
 * @param state - Form field state containing references to selected entities
 * @param detailRows - Array of detail row data
 * @param bankFeeRows - Array of bank fee row data
 * @param entityUnit - Entity unit information
 * @returns Transformed data ready for API submission
 */
export const transformFormData = (
  data: any,
  state: FormFieldState,
  detailRows: any[] = [],
  bankFeeRows: any[] = [],
  entityUnit: any
) => {
  const chi_tiet_giay_bao_co_items = transformDetailRows(detailRows, data.dien_giai);
  const phieu_ngan_hang_giay_bao_co_items = transformBankFeeRows(bankFeeRows);

  const firstDetailRow = detailRows?.[0];
  const ma_kh = firstDetailRow?.ma_kh_data?.uuid || firstDetailRow?.ma_kh || '';

  const { tong_tien, tong_thanh_toan } = calculatePaymentTotals(detailRows, bankFeeRows);

  return {
    ...data,
    unit_id: entityUnit?.uuid || '',

    ma_ngv: data.ma_ngv || '1',
    dia_chi: data.dia_chi || '',
    ong_ba: data.ong_ba || '',
    dien_giai: data.dien_giai || '',
    ma_httt: data.pc_tao_yn ? data.ma_httt : null,

    ma_kh,

    tknh: state.taiKhoanNganHang?.uuid || data.tknh || '',

    tk: state.taiKhoan?.uuid || data.tk || '',

    ...transformDocumentNumber(state.quyenChungTu, state.soChungTu || '', MA_CHUNG_TU.TIEN_GUI.GIAY_BAO_CO),

    ngay_ct: data.ngay_ct || '',
    ngay_lct: data.ngay_lct || '',

    ma_nt: data.ma_nt || 'VND',
    ty_gia: data.ty_gia || 1,
    status: data.status || '0',
    transfer_yn: data.transfer_yn || false,
    hd_yn: data.hd_yn || false,
    tg_dd: data.tg_dd || false,
    cltg_yn: data.cltg_yn || false,
    so_ct0: data.so_ct0 || '',
    ngay_ct0: data.ngay_ct0 || '',
    so_ct_goc: data.so_ct_goc || '',
    dien_giai_ct_goc: data.dien_giai_ct_goc || '',
    ma_tt: data.ma_tt || '',
    t_tien_nt: tong_tien,
    t_tien: tong_tien,
    chi_tiet_giay_bao_co_items,
    phieu_ngan_hang_giay_bao_co_items
  };
};

/**
 * Transform data for editing (reverse transformation)
 * @param apiData - Data from API response
 * @returns Transformed data for form initialization
 */
export const transformApiDataForEdit = (apiData: any) => {
  if (!apiData) return {};

  return {
    ...apiData,
    chi_tiet_data:
      apiData.chi_tiet_giay_bao_co_data?.map((item: any) => ({
        ...item,
        uuid: item.uuid || `temp-${Date.now()}-${Math.random()}`,
        ma_kh_data: item.ma_kh_data || null,
        tk_co_data: item.tk_co_data || null,
        ma_bp_data: item.ma_bp_data || null,
        ma_vv_data: item.ma_vv_data || null,
        ma_hd_data: item.ma_hd_data || null,
        id_hd_data: item.id_hd_data || null,
        ma_dtt_data: item.ma_dtt_data || null,
        ma_ku_data: item.ma_ku_data || null,
        ma_phi_data: item.ma_phi_data || null,
        ma_sp_data: item.ma_sp_data || null,
        ma_cp0_data: item.ma_cp0_data || null
      })) || [],
    chi_tiet_phi_data:
      apiData.phieu_ngan_hang_giay_bao_co_data?.map((item: any) => ({
        ...item,
        uuid: item.uuid || `temp-${Date.now()}-${Math.random()}`,
        ma_kh_data: item.ma_kh_data || null,
        ma_cpnh_data: item.ma_cpnh_data || null,
        so_ct0_data: item.so_ct0_data || null,
        so_ct2_data: item.so_ct2_data || null,
        tk_cpnh_data: item.tk_cpnh_data || null,
        tk_du_data: item.tk_du_data || null,
        tk_thue_data: item.tk_thue_data || null,
        ma_thue_data: item.ma_thue_data || null,
        ma_bp_data: item.ma_bp_data || null,
        ma_vv_data: item.ma_vv_data || null,
        ma_hd_data: item.ma_hd_data || null,
        ma_dtt_data: item.ma_dtt_data || null,
        ma_ku_data: item.ma_ku_data || null,
        ma_phi_data: item.ma_phi_data || null,
        ma_sp_data: item.ma_sp_data || null,
        ma_cp0_data: item.ma_cp0_data || null
      })) || []
  };
};

/**
 * Calculate totals from detail rows
 * @param detailRows - Array of detail row data
 * @returns Object containing calculated totals
 */
export const calculateDetailTotals = (detailRows: any[]) => {
  return detailRows.reduce(
    (totals, row) => ({
      t_tien_nt: totals.t_tien_nt + (Number(row.tien_nt) || 0),
      t_tien: totals.t_tien + (Number(row.tien) || 0)
    }),
    { t_tien_nt: 0, t_tien: 0 }
  );
};

/**
 * Calculate totals from bank fee rows
 * @param bankFeeRows - Array of bank fee row data
 * @returns Object containing calculated bank fee totals
 */
export const calculateBankFeeTotals = (bankFeeRows: any[]) => {
  return bankFeeRows.reduce(
    (totals, row) => ({
      t_cp_nt: totals.t_cp_nt + (Number(row.tien_cp_nt) || 0),
      t_cp: totals.t_cp + (Number(row.tien_cp) || 0),
      t_thue_nt: totals.t_thue_nt + (Number(row.t_thue_nt) || 0),
      t_thue: totals.t_thue + (Number(row.t_thue) || 0)
    }),
    { t_cp_nt: 0, t_cp: 0, t_thue_nt: 0, t_thue: 0 }
  );
};

/**
 * Validate form data before submission
 * @param data - Form data to validate
 * @param state - Form field state
 * @returns Validation result with errors if any
 */
export const validateFormData = (data: any, state: FormFieldState) => {
  const errors: string[] = [];

  if (!state.taiKhoan) {
    errors.push('Tài khoản là bắt buộc');
  }

  if (!data.ngay_ct) {
    errors.push('Ngày chứng từ là bắt buộc');
  }

  if (!data.ma_nt) {
    errors.push('Mã ngoại tệ là bắt buộc');
  }

  if (!data.ty_gia || Number(data.ty_gia) <= 0) {
    errors.push('Tỷ giá phải lớn hơn 0');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

export const transformDataFromHoaDon = (hoaDonData?: any) => {
  const baseData = {
    ma_ngv: '1',
    dia_chi: hoaDonData?.dia_chi || '',
    ong_ba: hoaDonData?.ong_ba || '',
    dien_giai: `Thu tiền: ${hoaDonData?.dien_giai || ''}`,
    ma_nt: hoaDonData?.ma_nt,
    ty_gia: hoaDonData?.ty_gia,
    status: '5',
    transfer_yn: true,
    tk_data: null,
    ngay_ct0: new Date().toISOString().split('T')[0],
    so_ct: null
  };
  const chi_tiet = hoaDonData && [
    {
      dien_giai: `Thu tiền: ${hoaDonData?.dien_giai || ''}`,
      ma_kh_data: hoaDonData?.ma_kh_data,
      id_hd_data: {
        ID: hoaDonData?.uuid || '',
        da_thanh_toan: 0,
        ma_ct: hoaDonData?.ma_ct || '',
        ngay_ct: hoaDonData?.ngay_ct || '',
        so_ct: hoaDonData?.so_ct || '',
        tien_con_phai_tt: hoaDonData?.t_tt || 0,
        tien_hd_nt: hoaDonData?.t_tt_nt || 0,
        tien_tren_hd: hoaDonData?.t_tt_nt || 0,
        con_lai: hoaDonData?.t_tt || 0,
        ngoai_te: hoaDonData?.ma_nt_data?.ma_nt || '',
        tk_data: {
          uuid: hoaDonData?.tk_data?.uuid || '',
          tk: hoaDonData?.tk_data?.code || ''
        }
      },
      tien_nt: hoaDonData.t_tt_nt,
      tien: hoaDonData.t_tt,
      ma_vv_data: hoaDonData.chi_tiet[0].ma_vv_data,
      ma_hd_data: hoaDonData.chi_tiet[0].ma_hd_data,
      ma_dtt_data: hoaDonData.chi_tiet[0].ma_dtt_data,
      ma_ku_data: hoaDonData.chi_tiet[0].ma_ku_data,
      ma_phi_data: hoaDonData.chi_tiet[0].ma_phi_data,
      ma_sp_data: hoaDonData.chi_tiet[0].ma_sp_data,
      ma_cp0_data: hoaDonData.chi_tiet[0].ma_cp0_data
    }
  ];

  return {
    ...baseData,
    chi_tiet_data: chi_tiet
  };
};
